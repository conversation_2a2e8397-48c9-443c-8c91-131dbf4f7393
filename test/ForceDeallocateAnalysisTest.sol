// SPDX-License-Identifier: GPL-2.0-or-later
pragma solidity 0.8.28;

import {BaseTest} from "./BaseTest.sol";
import {IVaultV2} from "../src/interfaces/IVaultV2.sol";
import {ErrorsLib} from "../src/libraries/ErrorsLib.sol";
import {EventsLib} from "../src/libraries/EventsLib.sol";
import {MathLib} from "../src/libraries/MathLib.sol";
import {AdapterMock} from "./mocks/AdapterMock.sol";
import "../src/libraries/ConstantsLib.sol";

/**
 * @title ForceDeallocateAnalysisTest
 * @notice Comprehensive analysis of the forceDeallocate function to understand the actual behavior
 */
contract ForceDeallocateAnalysisTest is BaseTest {
    using MathLib for uint256;

    AdapterMock adapter;
    address victim = makeAddr("victim");
    address attacker = makeAddr("attacker");
    
    function setUp() public override {
        super.setUp();

        // Setup adapter
        adapter = new AdapterMock(address(vault));
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setIsAdapter, (address(adapter), true)));
        vault.setIsAdapter(address(adapter), true);

        // Setup caps
        increaseAbsoluteCap("id-0", type(uint128).max);
        increaseAbsoluteCap("id-1", type(uint128).max);
        increaseRelativeCap("id-0", WAD);
        increaseRelativeCap("id-1", WAD);

        // Give tokens to participants
        deal(address(underlyingToken), victim, 1000e18);
        deal(address(underlyingToken), attacker, 100e18);
        
        // Approve vault
        vm.prank(victim);
        underlyingToken.approve(address(vault), type(uint256).max);
        vm.prank(attacker);
        underlyingToken.approve(address(vault), type(uint256).max);
    }

    /**
     * @notice Test the exact scenario from the working test
     * @dev This should PASS - replicating the working behavior
     */
    function testWorkingScenario() public {
        uint256 depositAmount = 1000e18;
        uint256 deallocateAmount = 100e18;
        uint256 penalty = 0.01e18; // 1%
        
        // Victim deposits funds
        vm.prank(victim);
        uint256 shares = vault.deposit(depositAmount, victim);
        
        // Allocate funds to adapter
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", depositAmount);
        
        // Set penalty
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setForceDeallocatePenalty, (address(adapter), penalty)));
        vault.setForceDeallocatePenalty(address(adapter), penalty);
        
        // Call forceDeallocate with victim as both caller and onBehalf (like working test)
        vm.prank(victim);
        uint256 penaltyShares = vault.forceDeallocate(address(adapter), hex"", deallocateAmount, victim);
        
        // This should work because msg.sender == onBehalf (no allowance check)
        assertGt(penaltyShares, 0, "Penalty should be applied");
    }

    /**
     * @notice Test the claimed vulnerability scenario
     * @dev This should FAIL due to allowance checks - disproving the vulnerability
     */
    function testClaimedVulnerabilityScenario() public {
        uint256 depositAmount = 1000e18;
        uint256 deallocateAmount = 100e18;
        uint256 penalty = 0.01e18; // 1%
        
        // Victim deposits funds
        vm.prank(victim);
        vault.deposit(depositAmount, victim);
        
        // Allocate funds to adapter
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", depositAmount);
        
        // Set penalty
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setForceDeallocatePenalty, (address(adapter), penalty)));
        vault.setForceDeallocatePenalty(address(adapter), penalty);
        
        // Verify victim has NOT approved attacker
        assertEq(vault.allowance(victim, attacker), 0, "Victim should not have approved attacker");
        
        // Attacker tries to call forceDeallocate targeting victim
        // This should FAIL due to allowance check, disproving the vulnerability
        vm.prank(attacker);
        vm.expectRevert(); // Should fail due to insufficient allowance
        vault.forceDeallocate(address(adapter), hex"", deallocateAmount, victim);
    }

    /**
     * @notice Test what happens when victim has approved attacker
     * @dev This should PASS - showing the function works when allowance exists
     */
    function testWithProperAllowance() public {
        uint256 depositAmount = 1000e18;
        uint256 deallocateAmount = 100e18;
        uint256 penalty = 0.01e18; // 1%
        
        // Victim deposits funds
        vm.prank(victim);
        vault.deposit(depositAmount, victim);
        
        // Allocate funds to adapter
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", depositAmount);
        
        // Set penalty
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setForceDeallocatePenalty, (address(adapter), penalty)));
        vault.setForceDeallocatePenalty(address(adapter), penalty);
        
        // Victim approves attacker (for whatever reason)
        uint256 penaltyAssets = deallocateAmount.mulDivUp(penalty, WAD);
        uint256 penaltyShares = vault.previewWithdraw(penaltyAssets);
        vm.prank(victim);
        vault.approve(attacker, penaltyShares);
        
        // Now attacker can call forceDeallocate
        vm.prank(attacker);
        uint256 actualPenaltyShares = vault.forceDeallocate(address(adapter), hex"", deallocateAmount, victim);
        
        // This should work because victim approved the attacker
        assertGt(actualPenaltyShares, 0, "Penalty should be applied");
        assertEq(actualPenaltyShares, penaltyShares, "Penalty should match expected");
    }

    /**
     * @notice Test access control comparison
     * @dev Shows that other functions DO have access control
     */
    function testAccessControlComparison() public {
        // forceDeallocate has no immediate access control check
        // It only fails later due to allowance (if any)
        
        // But allocate and deallocate DO have immediate access control
        vm.prank(attacker);
        vm.expectRevert(ErrorsLib.Unauthorized.selector);
        vault.allocate(address(adapter), hex"", 100e18);
        
        vm.prank(attacker);
        vm.expectRevert(ErrorsLib.Unauthorized.selector);
        vault.deallocate(address(adapter), hex"", 100e18);
    }

    /**
     * @notice Test to understand the design intent
     * @dev Analyzes what the function is supposed to do
     */
    function testDesignIntent() public {
        // The function seems designed to allow forced deallocation with penalty
        // But the penalty is applied to the onBehalf address
        // This suggests it should be called by the user themselves or with their permission
        
        uint256 depositAmount = 1000e18;
        uint256 deallocateAmount = 100e18;
        uint256 penalty = 0.01e18; // 1%
        
        // Setup
        vm.prank(victim);
        vault.deposit(depositAmount, victim);
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", depositAmount);
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setForceDeallocatePenalty, (address(adapter), penalty)));
        vault.setForceDeallocatePenalty(address(adapter), penalty);
        
        // Intended usage: User calls it on themselves
        vm.prank(victim);
        uint256 penaltyShares = vault.forceDeallocate(address(adapter), hex"", deallocateAmount, victim);
        
        assertGt(penaltyShares, 0, "Self-imposed penalty should work");
        
        // The penalty goes to the vault, not the caller
        // This suggests it's meant to discourage manipulation, not enable griefing
    }
}
