name: Publish on NPM

on:
  workflow_dispatch:

jobs:
  publish-to-npm:
    name: Publish to NPM
    runs-on: ubuntu-latest

    environment:
      name: npm
      url: https://www.npmjs.com/package/@morpho-org/morpho-blue

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Publish to npm
        run: |
          echo "//registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }}" > ~/.npmrc
          yarn publish --access public --ignore-scripts
