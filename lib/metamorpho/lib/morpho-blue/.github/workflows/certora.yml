name: Certora

on:
  push:
    branches:
      - main
  pull_request:
  workflow_dispatch:

jobs:
  verify:
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false

      matrix:
        conf:
          - AccrueInterest
          - AssetsAccounting
          - ConsistentState
          - ExactMath
          - ExchangeRate
          - Health
          - LibSummary
          - Liveness
          - Reentrancy
          - Reverts
          - StayHealthy
          - Transfer

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Install python
        uses: actions/setup-python@v5
        with:
          python-version: ">=3.9"

      - name: Install certora
        run: pip install certora-cli

      - name: Install solc
        run: |
          wget https://github.com/ethereum/solidity/releases/download/v0.8.19/solc-static-linux
          chmod +x solc-static-linux
          sudo mv solc-static-linux /usr/local/bin/solc-0.8.19

      - name: Verify ${{ matrix.conf }}
        run: certoraRun certora/confs/${{ matrix.conf }}.conf
        env:
          CERTORAKEY: ${{ secrets.CERTORAKEY }}
