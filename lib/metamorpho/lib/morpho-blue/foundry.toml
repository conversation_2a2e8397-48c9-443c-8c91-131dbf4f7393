[profile.default]
libs = ["lib"]
names = true
sizes = true
via_ir = true
optimizer = true
optimizer_runs = 999999
bytecode_hash = "none"
evm_version = "paris"

[profile.default.invariant]
runs = 16
depth = 256
fail_on_revert = true

[profile.default.fmt]
wrap_comments = true


[profile.build]
test = "/dev/null"
script = "/dev/null"


[profile.test]
via-ir = false


# See more config options https://github.com/foundry-rs/foundry/tree/master/crates/config
