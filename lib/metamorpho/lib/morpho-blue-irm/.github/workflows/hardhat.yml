name: Hardhat

on:
  push:
    branches:
      - main
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.event_name }}
  cancel-in-progress: true

jobs:
  yarn-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: recursive

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1

      - name: Install node
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: yarn

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Save hardhat cache
        uses: actions/cache@v3
        with:
          path: |
            cache_hardhat
            artifacts
          key: ${{ github.ref_name }}-hardhat

      - name: Run Hardhat tests
        run: yarn test:hardhat
